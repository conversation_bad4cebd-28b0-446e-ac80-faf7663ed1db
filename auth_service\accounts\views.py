from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.http import urlsafe_base64_decode
from django.contrib.auth.tokens import default_token_generator
from django.http import Http404
from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>enticated, IsAdminUser
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.cache import cache
from django.conf import settings
from .models import User, FailedLoginAttempt, Address
from .serializers import (
    UserSerializer,
    UserRegistrationSerializer,
    OTPVerificationSerializer,
    StaffLoginSerializer,
    CustomerProviderLoginSerializer,
    PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer,
    AddressSerializer,
    ResendOTPSerializer,
    LockedAccountSerializer,
    AdminUnlockAccountSerializer,
    AdminResetOTPLimitSerializer
)
from utils.msg91_service import send_otp, verify_otp, resend_otp
from django_ratelimit.decorators import ratelimit
from .utils import clear_failed_attempts

User = get_user_model()

# Rate limit for OTP sending
class CustomerProviderRegisterView(generics.CreateAPIView):
    serializer_class = UserRegistrationSerializer

    @ratelimit(key='ip', rate='5/h', method='POST')
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 201:
            return Response({'message': 'OTP sent successfully'})
        return response

class VerifyRegistrationOTPView(generics.CreateAPIView):
    serializer_class = OTPVerificationSerializer

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            return Response({'message': 'Registration successful', 'access': response.data['access'], 'refresh': response.data['refresh']})
        return response

class StaffLoginView(generics.CreateAPIView):
    serializer_class = StaffLoginSerializer

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            return Response({'message': 'Login successful', 'access': response.data['access'], 'refresh': response.data['refresh']})
        return response

class LogoutView(generics.DestroyAPIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        try:
            refresh_token = request.data["refresh_token"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(status=status.HTTP_205_RESET_CONTENT)
        except Exception:
            return Response(status=status.HTTP_400_BAD_REQUEST)

class CustomTokenRefreshView(generics.CreateAPIView):
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            user = request.user
            user_data = UserSerializer(user).data
            response.data['user'] = user_data
        return response

class UserProfileView(generics.RetrieveUpdateAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = UserSerializer

    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

class VerifyUserView(generics.CreateAPIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        user = request.user
        if user.user_type in [User.UserType.CUSTOMER, User.UserType.PROVIDER]:
            mobile_number = user.mobile_number
            response = send_otp(mobile_number)
            if response.get('type') != 'success':
                return Response({'error': 'OTP send failed'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            cache.set(f'verify_{mobile_number}', True, timeout=300)
            return Response({'message': 'OTP sent successfully'})
        return Response({'error': 'Invalid user type'}, status=status.HTTP_400_BAD_REQUEST)

class VerifyUserOTPView(generics.CreateAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = OTPVerificationSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        data = serializer.validated_data
        user = request.user
        
        if data['mobile_number'] != user.mobile_number:
            return Response({'error': 'Invalid mobile number'}, status=status.HTTP_400_BAD_REQUEST)
        
        verify_response = verify_otp(data['mobile_number'], data['otp'])
        if verify_response.get('type') != 'success':
            return Response({'error': 'Invalid OTP'}, status=status.HTTP_400_BAD_REQUEST)
        
        user.is_verified = True
        user.save()
        return Response({'message': 'User verified successfully'})

class PasswordResetRequestView(generics.CreateAPIView):
    serializer_class = PasswordResetRequestSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        email = serializer.validated_data['email']
        try:
            user = User.objects.get(email=email, user_type=User.UserType.STAFF)
            user.send_password_reset_email(request)
            return Response({'message': 'Password reset email sent'})
        except User.DoesNotExist:
            return Response({'error': 'No staff user with that email'}, status=status.HTTP_404_NOT_FOUND)

class PasswordResetConfirmView(generics.CreateAPIView):
    serializer_class = PasswordResetConfirmSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        data = serializer.validated_data
        try:
            uid = urlsafe_base64_decode(data['uid']).decode()
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            user = None
        
        if user and default_token_generator.check_token(user, data['token']):
            user.set_password(data['password'])
            user.save()
            return Response({'message': 'Password reset successful'})
        return Response({'error': 'Invalid token'}, status=status.HTTP_400_BAD_REQUEST)

class CustomerProviderLoginView(generics.CreateAPIView):
    serializer_class = CustomerProviderLoginSerializer

    @ratelimit(key='ip', rate='5/h', method='POST')
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            return Response({'message': 'OTP sent successfully'})
        return response

class VerifyLoginOTPView(generics.CreateAPIView):
    serializer_class = OTPVerificationSerializer

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            return Response({'message': 'Login successful', 'access': response.data['access'], 'refresh': response.data['refresh']})
        return response

class ResendOTPView(generics.CreateAPIView):
    serializer_class = ResendOTPSerializer

    @ratelimit(key='ip', rate='3/h', method='POST')
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            return Response({'message': 'OTP resent successfully'})
        return response

class AdminLockedAccountsView(generics.ListAPIView):
    permission_classes = (IsAuthenticated, IsAdminUser)
    serializer_class = LockedAccountSerializer

    def get(self, request, *args, **kwargs):
        """List all locked accounts"""
        locked_users = User.objects.filter(is_locked=True)
        serializer = self.get_serializer(locked_users, many=True)
        return Response(serializer.data)

class AdminUnlockAccountView(generics.CreateAPIView):
    permission_classes = (IsAuthenticated, IsAdminUser)
    serializer_class = AdminUnlockAccountSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=serializer.validated_data['user_id'])
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

        # Unlock the account
        user.is_locked = False
        user.lockout_until = None
        user.save()

        # Clear failed attempts
        clear_failed_attempts(user=user)

        # If requested, also reset OTP rate limit
        if serializer.validated_data.get('unlock_otp_limit'):
            cache.delete(f'otp_limit_{user.mobile_number}')

        return Response({'message': 'Account unlocked successfully'})

class AdminResetOTPLimitView(generics.CreateAPIView):
    permission_classes = (IsAuthenticated, IsAdminUser)
    serializer_class = AdminResetOTPLimitSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        mobile_number = serializer.validated_data['mobile_number']
        
        # Check if user exists
        if not User.objects.filter(mobile_number=mobile_number).exists():
            return Response(
                {'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

        # Reset OTP rate limit
        cache.delete(f'otp_limit_{mobile_number}')

        return Response({'message': 'OTP limit reset successfully'})

class AddressListView(generics.ListCreateAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = AddressSerializer

    def get(self, request, *args, **kwargs):
        """List all addresses for the authenticated user"""
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """Add a new address for the authenticated user"""
        return super().post(request, *args, **kwargs)

class AddressDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = AddressSerializer

    def get_object(self, pk, user):
        try:
            return Address.objects.get(pk=pk, user=user)
        except Address.DoesNotExist:
            raise Http404

    def get(self, request, pk, *args, **kwargs):
        """Get a specific address"""
        address = self.get_object(pk, request.user)
        serializer = self.get_serializer(address)
        return Response(serializer.data)

    def put(self, request, pk, *args, **kwargs):
        """Update a specific address"""
        address = self.get_object(pk, request.user)
        serializer = self.get_serializer(address, data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        serializer.save()
        return Response(self.get_serializer(address).data)

    def delete(self, request, pk, *args, **kwargs):
        """Delete a specific address"""
        address = self.get_object(pk, request.user)
        
        # If this is the default address and there are other addresses,
        # make the most recent address the default
        if address.is_default and request.user.addresses.count() > 1:
            next_address = request.user.addresses.exclude(pk=pk).first()
            if next_address:
                next_address.is_default = True
                next_address.save()
        
        address.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
