from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from datetime import timedelta
from django.core.cache import cache
from .models import User, FailedLoginAttempt

def get_tokens_for_user(user):
    refresh = RefreshToken.for_user(user)
    
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
        'user_type': user.user_type,
        'name': user.name
    }

def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def record_failed_attempt(request, user=None, mobile_number=None, email=None):
    ip_address = get_client_ip(request)
    FailedLoginAttempt.objects.create(
        user=user,
        mobile_number=mobile_number,
        email=email,
        ip_address=ip_address
    )

def check_account_locked(user):
    if not user.is_locked:
        return False
    
    if user.lockout_until and user.lockout_until > timezone.now():
        return True
    
    # If lockout period has expired, unlock the account
    user.is_locked = False
    user.lockout_until = None
    user.save()
    return False

def handle_failed_attempt(request, user=None, mobile_number=None, email=None):
    MAX_ATTEMPTS = 5
    LOCKOUT_DURATION = timedelta(minutes=30)
    
    # Record the failed attempt
    record_failed_attempt(request, user, mobile_number, email)
    
    # Get recent failed attempts
    recent_attempts = FailedLoginAttempt.objects.filter(
        timestamp__gte=timezone.now() - timedelta(minutes=30)
    )
    
    if user:
        recent_attempts = recent_attempts.filter(user=user)
    elif mobile_number:
        recent_attempts = recent_attempts.filter(mobile_number=mobile_number)
    elif email:
        recent_attempts = recent_attempts.filter(email=email)
    
    # Check if we should lock the account
    if recent_attempts.count() >= MAX_ATTEMPTS:
        if user:
            user.is_locked = True
            user.lockout_until = timezone.now() + LOCKOUT_DURATION
            user.save()
            return True, f"Account locked for {LOCKOUT_DURATION.total_seconds() / 60} minutes due to too many failed attempts"
        else:
            # For non-user attempts (e.g., during registration), we can use cache
            cache_key = f"lockout_{mobile_number or email}"
            cache.set(cache_key, True, timeout=int(LOCKOUT_DURATION.total_seconds()))
            return True, f"Too many failed attempts. Please try again in {LOCKOUT_DURATION.total_seconds() / 60} minutes"
    
    return False, None

def clear_failed_attempts(user=None, mobile_number=None, email=None):
    FailedLoginAttempt.objects.filter(
        user=user,
        mobile_number=mobile_number,
        email=email
    ).delete()
    
    if user:
        user.is_locked = False
        user.lockout_until = None
        user.save()
    else:
        cache_key = f"lockout_{mobile_number or email}"
        cache.delete(cache_key)