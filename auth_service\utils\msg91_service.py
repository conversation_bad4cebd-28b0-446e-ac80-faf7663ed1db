import requests
import os
from django.conf import settings

def send_otp(mobile_number):
    if not settings.MSG91_AUTH_KEY or not settings.MSG91_TEMPLATE_ID:
        raise ValueError("MSG91_AUTH_KEY and MSG91_TEMPLATE_ID must be set in settings.")
    url = "https://control.msg91.com/api/v5/otp"
    payload = {
        "template_id": settings.MSG91_TEMPLATE_ID,
        "mobile": mobile_number
    }
    headers = {
        "authkey": settings.MSG91_AUTH_KEY,
        "Content-Type": "application/json"
    }
    response = requests.post(url, json=payload, headers=headers)
    return response.json()

def verify_otp(mobile_number, otp):
    if not settings.MSG91_AUTH_KEY:
        raise ValueError("MSG91_AUTH_KEY must be set in settings.")
    url = f"https://control.msg91.com/api/v5/otp/verify"
    params = {
        "mobile": mobile_number,
        "otp": otp
    }
    headers = {"authkey": settings.MSG91_AUTH_KEY}
    response = requests.get(url, params=params, headers=headers)
    return response.json()

def resend_otp(mobile_number, retry_type="text"):
    if not settings.MSG91_AUTH_KEY:
        raise ValueError("MSG91_AUTH_KEY must be set in settings.")
    url = "https://control.msg91.com/api/v5/otp/retry"
    params = {
        "mobile": mobile_number,
        "retrytype": retry_type
    }
    headers = {"authkey": settings.MSG91_AUTH_KEY}
    response = requests.get(url, params=params, headers=headers)
    return response.json()