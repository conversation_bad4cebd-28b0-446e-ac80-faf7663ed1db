from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()

class Command(BaseCommand):
    help = 'Ensures at least one superuser exists'

    def handle(self, *args, **options):
        if not User.objects.filter(is_superuser=True).exists():
            email = input("Enter superuser email: ")
            name = input("Enter superuser name: ")
            password = input("Enter superuser password: ")
            
            User.objects.create_superuser(
                email=email,
                name=name,
                password=password,
                user_type=User.UserType.STAFF
            )
            self.stdout.write(self.style.SUCCESS('Superuser created successfully'))
        else:
            self.stdout.write(self.style.SUCCESS('Superuser already exists'))