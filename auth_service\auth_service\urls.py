"""
URL configuration for auth_service project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import TokenRefreshView
from accounts.views import (
    CustomerProviderRegisterView,
    VerifyRegistrationOTPView,
    StaffLoginView,
    CustomerProviderLoginView,
    VerifyLoginOTPView,
    ResendOTPView,
    PasswordResetRequestView,
    PasswordResetConfirmView,
    AddressListView,
    AddressDetailView,
    AdminLockedAccountsView,
    AdminUnlockAccountView,
    AdminResetOTPLimitView
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # Auth endpoints
    path('auth/register/', CustomerProviderRegisterView.as_view(), name='register'),
    path('auth/verify-registration/', VerifyRegistrationOTPView.as_view(), name='verify-registration'),
    path('auth/staff/login/', StaffLoginView.as_view(), name='staff-login'),
    path('auth/login/', CustomerProviderLoginView.as_view(), name='login'),
    path('auth/verify-login/', VerifyLoginOTPView.as_view(), name='verify-login'),
    path('auth/resend-otp/', ResendOTPView.as_view(), name='resend-otp'),
    path('auth/password-reset/', PasswordResetRequestView.as_view(), name='password-reset'),
    path('auth/password-reset/confirm/', PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
    
    # Address management
    path('auth/addresses/', AddressListView.as_view(), name='address-list'),
    path('auth/addresses/<int:pk>/', AddressDetailView.as_view(), name='address-detail'),
    
    # Admin endpoints
    path('auth/admin/locked-accounts/', AdminLockedAccountsView.as_view(), name='admin-locked-accounts'),
    path('auth/admin/unlock-account/', AdminUnlockAccountView.as_view(), name='admin-unlock-account'),
    path('auth/admin/reset-otp-limit/', AdminResetOTPLimitView.as_view(), name='admin-reset-otp-limit'),
]

# Error handlers
handler400 = 'auth_service.views.handler400'
handler403 = 'auth_service.views.handler403'
handler404 = 'auth_service.views.handler404'
handler500 = 'auth_service.views.handler500'

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)