import os
import django
from django.conf import settings
from django.db import connections
from django.db.utils import OperationalError

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auth_service.settings')
django.setup()

def test_db_connection():
    try:
        # Get the default database connection
        conn = connections['default']
        # Try to connect
        conn.cursor()
        print("✅ Database connection successful!")
        print(f"Database: {settings.DATABASES['default']['NAME']}")
        print(f"Host: {settings.DATABASES['default']['HOST']}")
        print(f"Port: {settings.DATABASES['default']['PORT']}")
        print(f"User: {settings.DATABASES['default']['USER']}")
    except OperationalError as e:
        print("❌ Database connection failed!")
        print(f"Error: {str(e)}")
    except Exception as e:
        print("❌ An error occurred!")
        print(f"Error: {str(e)}")

if __name__ == '__main__':
    test_db_connection() 