from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from .models import User
from django.core.cache import cache
from unittest.mock import patch

class AuthenticationTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            name='Staff User',
            password='staffpass123',
            user_type=User.UserType.STAFF,
            is_verified=True
        )
        self.customer_user = User.objects.create_user(
            mobile_number='+1234567890',
            name='Customer User',
            user_type=User.UserType.CUSTOMER
        )

    def test_staff_login(self):
        url = reverse('staff-login')
        data = {
            'email': '<EMAIL>',
            'password': 'staffpass123'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

    def test_staff_login_invalid_credentials(self):
        url = reverse('staff-login')
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpass'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch('utils.msg91_service.send_otp')
    def test_customer_registration(self, mock_send_otp):
        mock_send_otp.return_value = {'type': 'success'}
        url = reverse('register')
        data = {
            'mobile_number': '+9876543210',
            'name': 'New Customer',
            'user_type': User.UserType.CUSTOMER
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'OTP sent successfully')

    def test_customer_registration_duplicate_mobile(self):
        url = reverse('register')
        data = {
            'mobile_number': '+1234567890',  # Already exists
            'name': 'Duplicate Customer',
            'user_type': User.UserType.CUSTOMER
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch('utils.msg91_service.verify_otp')
    def test_verify_registration_otp(self, mock_verify_otp):
        mock_verify_otp.return_value = {'type': 'success'}
        # First register
        mobile_number = '+9876543210'
        cache.set(f'reg_{mobile_number}', {
            'name': 'New Customer',
            'user_type': User.UserType.CUSTOMER
        }, timeout=300)

        url = reverse('verify-registration')
        data = {
            'mobile_number': mobile_number,
            'otp': '123456'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

    def test_user_profile(self):
        # Login first
        self.client.force_authenticate(user=self.staff_user)
        
        url = reverse('profile')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['name'], 'Staff User')

    def test_update_user_profile(self):
        self.client.force_authenticate(user=self.staff_user)
        
        url = reverse('profile')
        data = {
            'name': 'Updated Staff Name'
        }
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Staff Name')

    def test_logout(self):
        # Login first
        self.client.force_authenticate(user=self.staff_user)
        
        url = reverse('logout')
        data = {
            'refresh_token': 'dummy_refresh_token'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_205_RESET_CONTENT)

    @patch('utils.msg91_service.send_otp')
    def test_verify_user(self, mock_send_otp):
        mock_send_otp.return_value = {'type': 'success'}
        self.client.force_authenticate(user=self.customer_user)
        
        url = reverse('verify-user')
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'OTP sent successfully')

    @patch('utils.msg91_service.verify_otp')
    def test_verify_user_otp(self, mock_verify_otp):
        mock_verify_otp.return_value = {'type': 'success'}
        self.client.force_authenticate(user=self.customer_user)
        
        url = reverse('verify-user-otp')
        data = {
            'mobile_number': '+1234567890',
            'otp': '123456'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'User verified successfully')
        
        # Verify user is marked as verified
        self.customer_user.refresh_from_db()
        self.assertTrue(self.customer_user.is_verified)
