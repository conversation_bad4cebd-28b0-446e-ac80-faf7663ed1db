from django.shortcuts import render
from django.http import JsonResponse
from rest_framework import status

def handler404(request, exception):
    return JsonResponse({
        'error': 'Not Found',
        'message': 'The requested resource was not found on this server.'
    }, status=status.HTTP_404_NOT_FOUND)

def handler500(request):
    return JsonResponse({
        'error': 'Internal Server Error',
        'message': 'An unexpected error occurred. Please try again later.'
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def handler403(request, exception):
    return JsonResponse({
        'error': 'Forbidden',
        'message': 'You do not have permission to access this resource.'
    }, status=status.HTTP_403_FORBIDDEN)

def handler400(request, exception):
    return JsonResponse({
        'error': 'Bad Request',
        'message': 'The request could not be understood or was missing required parameters.'
    }, status=status.HTTP_400_BAD_REQUEST) 