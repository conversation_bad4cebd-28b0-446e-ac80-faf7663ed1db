from django.contrib.auth.backends import ModelBackend
from .models import User

class EmailBackend(ModelBackend):
    def authenticate(self, request, email=None, password=None, **kwargs):
        try:
            user = User.objects.get(email=email)
            if user.check_password(password):
                return user
        except User.DoesNotExist:
            return None

class MobileBackend(ModelBackend):
    def authenticate(self, request, mobile_number=None, **kwargs):
        try:
            return User.objects.get(mobile_number=mobile_number)
        except User.DoesNotExist:
            return None