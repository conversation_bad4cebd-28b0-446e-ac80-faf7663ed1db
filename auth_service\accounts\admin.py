from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User

class CustomUserAdmin(UserAdmin):
    list_display = ('email', 'mobile_number', 'name', 'user_type', 'is_staff', 'is_superuser')
    list_filter = ('user_type', 'is_staff', 'is_superuser')
    fieldsets = (
        (None, {'fields': ('email', 'mobile_number', 'name', 'password')}),
        ('Permissions', {'fields': ('user_type', 'is_active', 'is_staff', 'is_superuser')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'mobile_number', 'name', 'password1', 'password2', 'user_type', 'is_staff', 'is_superuser'),
        }),
    )
    search_fields = ('email', 'mobile_number', 'name')
    ordering = ('email',)

    def delete_model(self, request, obj):
        if obj.is_superuser and User.objects.filter(is_superuser=True).count() == 1:
            self.message_user(request, "Cannot delete the last super admin", level=40)
            return
        super().delete_model(request, obj)

admin.site.register(User, CustomUserAdmin)