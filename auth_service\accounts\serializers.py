from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
import re
from .models import User, Address

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'mobile_number', 'name', 'user_type', 'is_active', 'date_joined')
        read_only_fields = ('id', 'date_joined')

class UserProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('name', 'profile_picture')

class UserRegistrationSerializer(serializers.Serializer):
    mobile_number = serializers.CharField(max_length=15)
    name = serializers.CharField(max_length=255)
    user_type = serializers.ChoiceField(choices=User.UserType.choices)

class OTPVerificationSerializer(serializers.Serializer):
    mobile_number = serializers.Char<PERSON>ield(max_length=15)
    otp = serializers.CharField(max_length=4, min_length=4)

    def validate_otp(self, value):
        if not value.isdigit():
            raise serializers.ValidationError("OTP must contain only digits")
        if len(value) != 4:
            raise serializers.ValidationError("OTP must be exactly 4 digits")
        return value

class StaffLoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

class CustomerProviderLoginSerializer(serializers.Serializer):
    mobile_number = serializers.CharField(max_length=15)

class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()

class PasswordResetConfirmSerializer(serializers.Serializer):
    token = serializers.CharField()
    uidb64 = serializers.CharField()
    password = serializers.CharField(write_only=True)

    def validate_password(self, value):
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        return value

class LockedAccountSerializer(serializers.ModelSerializer):
    failed_attempts_count = serializers.SerializerMethodField()
    last_failed_attempt = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id', 'email', 'mobile_number', 'name', 'user_type', 
                 'is_locked', 'lockout_until', 'failed_attempts_count', 
                 'last_failed_attempt')

    def get_failed_attempts_count(self, obj):
        from .models import FailedLoginAttempt
        from django.utils import timezone
        from datetime import timedelta
        return FailedLoginAttempt.objects.filter(
            user=obj,
            timestamp__gte=timezone.now() - timedelta(minutes=30)
        ).count()

    def get_last_failed_attempt(self, obj):
        from .models import FailedLoginAttempt
        last_attempt = FailedLoginAttempt.objects.filter(user=obj).order_by('-timestamp').first()
        return last_attempt.timestamp if last_attempt else None

class AdminUnlockAccountSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    reason = serializers.CharField(max_length=500, required=False)
    unlock_otp_limit = serializers.BooleanField(default=False)

class AdminResetOTPLimitSerializer(serializers.Serializer):
    mobile_number = serializers.CharField(max_length=15)
    reason = serializers.CharField(max_length=500, required=False)

class ResendOTPSerializer(serializers.Serializer):
    mobile_number = serializers.CharField(max_length=15)
    retry_type = serializers.ChoiceField(choices=['text', 'voice'], default='text')

class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = ('id', 'address_type', 'street', 'city', 'state', 
                 'zip_code', 'landmark', 'is_default', 'created_at', 'updated_at')
        read_only_fields = ('id', 'created_at', 'updated_at')

    def validate(self, data):
        user = self.context['request'].user
        is_default = data.get('is_default', False)
        
        # If this is the first address, make it default
        if not user.addresses.exists() and not is_default:
            data['is_default'] = True
            
        return data

class AddressUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = ('address_type', 'street', 'city', 'state', 
                 'zip_code', 'landmark', 'is_default') 